import os
import time
import asyncio
import logging
import json
from typing import List, Dict, Any, Optional, AsyncGenerator, Callable
from dotenv import load_dotenv
import aiohttp

from tts_demo.config.constants import DEFAULT_LANGFLOW_URL

# 配置日志
logger = logging.getLogger("LangflowClient")

# 加载环境变量
load_dotenv()

class LangflowClient:
    """Langflow API客户端，支持流式输出"""

    def __init__(
        self,
        base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        chat_flow_id: Optional[str] = None,
        operator_flow_ids: Optional[List[str]] = None
    ):
        """
        初始化Langflow客户端

        参数:
            base_url: Langflow服务器基础URL，如果为None则从环境变量获取
            api_key: Langflow API密钥，如果为None则从环境变量获取
            chat_flow_id: 聊天工作流ID，如果为None则从环境变量获取
            operator_flow_ids: 操作工作流ID列表，可选参数
        """
        self.base_url = base_url or os.environ.get("LANGFLOW_BASE_URL", DEFAULT_LANGFLOW_URL)
        self.api_key = api_key or os.environ.get("LANGFLOW_API_KEY")
        self.chat_flow_id = chat_flow_id or os.environ.get("LANGFLOW_CHAT_FLOW_ID") or os.environ.get("LANGFLOW_FLOW_ID")
        self.operator_flow_ids = operator_flow_ids or []

        if not self.chat_flow_id:
            raise ValueError(
                "未提供Langflow聊天工作流ID。请通过参数提供、设置LANGFLOW_CHAT_FLOW_ID环境变量或在.env文件中配置。"
            )

        # 构建API端点
        self.chat_run_endpoint = f"{self.base_url}/api/v1/run/{self.chat_flow_id}"

        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "accept": "application/json"
        }

        if self.api_key:
            self.headers["x-api-key"] = self.api_key

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        stream: bool = True,
        session_id: Optional[str] = None,
        input_type: str = "chat",
        output_type: str = "chat",
        output_component: str = "",
        tweaks: Optional[Dict] = None,
        on_first_token: Optional[Callable] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        创建聊天完成请求

        参数:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}]
            stream: 是否使用流式输出，默认为True
            session_id: 会话ID，用于保持对话上下文
            input_type: 输入类型，默认为"chat"
            output_type: 输出类型，默认为"chat"
            output_component: 目标输出组件
            tweaks: 组件调整参数
            on_first_token: 收到第一个token时的回调函数
            **kwargs: 其他参数

        返回:
            流式输出的异步生成器
        """
        # 记录请求开始时间
        logger.info(f"开始请求Langflow工作流，Chat Flow ID: {self.chat_flow_id}, Operator Flow IDs: {self.operator_flow_ids}, Stream: {stream}")

        # 从消息列表中提取最后一条用户消息作为输入
        input_value = ""
        if messages:
            # 查找最后一条用户消息
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    input_value = msg.get("content", "")
                    break

        # 构建请求数据
        request_data = {
            "input_value": input_value,
            "input_type": input_type,
            "output_type": output_type,
            "output_component": output_component,
            "session_id": session_id or f"session-{int(time.time())}",
            "tweaks": tweaks
        }

        # 准备所有要请求的工作流
        all_flow_ids = [self.chat_flow_id]
        if self.operator_flow_ids:
            all_flow_ids.extend(self.operator_flow_ids)

        # 并行请求所有工作流，但只返回聊天工作流的响应
        async for chunk in self._handle_parallel_requests(all_flow_ids, request_data, stream, on_first_token):
            yield chunk

    async def _handle_parallel_requests(
        self,
        flow_ids: List[str],
        request_data: Dict[str, Any],
        stream: bool,
        on_first_token: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """并行处理多个工作流请求，只返回聊天工作流的响应"""

        # 为每个工作流创建URL
        urls = []
        for flow_id in flow_ids:
            url = f"{self.base_url}/api/v1/run/{flow_id}"
            if stream:
                url += "?stream=true"
            urls.append(url)

        # 启动操作工作流任务（后台执行）
        operator_tasks = []
        for i, (flow_id, url) in enumerate(zip(flow_ids[1:], urls[1:]), 1):
            task = asyncio.create_task(
                self._execute_operator_flow(url, request_data, flow_id)
            )
            operator_tasks.append(task)

        # 直接处理聊天工作流并返回响应
        chat_url = urls[0]
        if stream:
            async for chunk in self._handle_stream_response(chat_url, request_data, on_first_token):
                yield chunk
        else:
            async for chunk in self._handle_non_stream_response(chat_url, request_data, on_first_token):
                yield chunk

    async def _execute_operator_flow(
        self,
        url: str,
        request_data: Dict[str, Any],
        flow_id: str
    ):
        """执行操作工作流（后台执行，不返回响应）"""
        try:
            logger.info(f"开始执行操作工作流: {flow_id}")
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=request_data, headers=self.headers) as response:
                    if response.status == 200:
                        logger.info(f"操作工作流 {flow_id} 执行成功")
                        # 读取响应但不处理
                        await response.read()
                    else:
                        error_text = await response.text()
                        logger.warning(f"操作工作流 {flow_id} 执行失败: {response.status} - {error_text}")
        except Exception as e:
            logger.error(f"操作工作流 {flow_id} 执行异常: {e}")

    async def _handle_stream_response(
        self,
        url: str,
        request_data: Dict[str, Any],
        on_first_token: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理流式响应"""
        first_token_received = False

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=request_data, headers=self.headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Langflow API请求失败: {response.status} - {error_text}")

                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue

                    try:
                        # 解析JSON响应
                        event_data = json.loads(line)

                        # 检查是否是token事件
                        if event_data.get("event") == "token":
                            token_data = event_data.get("data", {})
                            chunk_content = token_data.get("chunk", "")

                            if chunk_content and not first_token_received:
                                first_token_received = True
                                first_token_time = time.time()

                                # 格式化时间戳
                                timestamp = time.strftime("%H:%M:%S", time.localtime(first_token_time))
                                ms = int((first_token_time - int(first_token_time)) * 1000)
                                formatted_time = f"{timestamp}.{ms:03d}"

                                logger.info(f"LLM返回第一个token: {formatted_time}")

                                # 调用回调函数
                                if on_first_token:
                                    on_first_token(first_token_time)

                            # 构造类似OpenAI的响应格式
                            yield self._create_chunk_response(chunk_content, token_data.get("id"))

                        elif event_data.get("event") == "end":
                            # 流式响应结束
                            break

                    except json.JSONDecodeError:
                        # 跳过无法解析的行
                        continue

    async def _handle_non_stream_response(
        self,
        url: str,
        request_data: Dict[str, Any],
        on_first_token: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理非流式响应"""
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=request_data, headers=self.headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Langflow API请求失败: {response.status} - {error_text}")

                result = await response.json()

                # 记录第一个token时间
                if on_first_token:
                    first_token_time = time.time()
                    timestamp = time.strftime("%H:%M:%S", time.localtime(first_token_time))
                    ms = int((first_token_time - int(first_token_time)) * 1000)
                    formatted_time = f"{timestamp}.{ms:03d}"
                    logger.info(f"LLM返回第一个token: {formatted_time}")
                    on_first_token(first_token_time)

                # 从结果中提取文本内容
                content = self._extract_content_from_result(result)
                if content:
                    yield self._create_chunk_response(content)

    def _create_chunk_response(self, content: str, message_id: Optional[str] = None) -> Dict[str, Any]:
        """创建类似OpenAI的chunk响应格式"""
        return {
            "choices": [{
                "delta": {
                    "content": content
                },
                "index": 0,
                "finish_reason": None
            }],
            "id": message_id or f"langflow-{int(time.time())}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": "langflow-workflow"
        }

    def _extract_content_from_result(self, result: Dict[str, Any]) -> Optional[str]:
        """从Langflow结果中提取文本内容"""
        try:
            # 尝试从不同的可能位置提取内容
            outputs = result.get("outputs", [])
            if outputs and len(outputs) > 0:
                output = outputs[0]
                if "outputs" in output:
                    # 查找消息内容
                    for output_item in output["outputs"]:
                        if "results" in output_item and "message" in output_item["results"]:
                            message = output_item["results"]["message"]
                            return message.get("text", "")
                        elif "message" in output_item:
                            return output_item["message"].get("text", "")

            return None
        except (KeyError, IndexError, TypeError):
            return None

    @staticmethod
    def extract_content(chunk: Dict[str, Any]) -> Optional[str]:
        """
        从Langflow流式响应中提取文本内容

        参数:
            chunk: Langflow流式响应块

        返回:
            提取的文本内容，如果没有内容则返回None
        """
        try:
            return chunk["choices"][0]["delta"]["content"]
        except (KeyError, IndexError, TypeError):
            return None

# 示例使用
async def example_usage():
    client = LangflowClient()
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手。"},
        {"role": "user", "content": "请简要介绍Python语言的特点。"}
    ]

    stream = await client.chat_completion(messages)

    full_response = ""
    async for chunk in stream:
        content = client.extract_content(chunk)
        if content:
            full_response += content
            print(content, end="", flush=True)

    print("\n\n完整响应:", full_response)

if __name__ == "__main__":
    asyncio.run(example_usage())
